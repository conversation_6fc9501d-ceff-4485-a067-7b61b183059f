Stack trace:
Frame         Function      Args
0007FFFF9960  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF9960, 0007FFFF8860) msys-2.0.dll+0x2118E
0007FFFF9960  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFF9960  0002100469F2 (00021028DF99, 0007FFFF9818, 0007FFFF9960, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9960  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9960  00021006A545 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDE3EE0000 ntdll.dll
7FFDE2F80000 KERNEL32.DLL
7FFDE1670000 KERNELBASE.dll
7FFDE1C80000 USER32.dll
7FFDE1BC0000 win32u.dll
000210040000 msys-2.0.dll
7FFDE2B50000 GDI32.dll
7FFDE1030000 gdi32full.dll
7FFDE1440000 msvcp_win.dll
7FFDE12F0000 ucrtbase.dll
7FFDE3220000 advapi32.dll
7FFDE2B80000 msvcrt.dll
7FFDE2A80000 sechost.dll
7FFDE3050000 RPCRT4.dll
7FFDE0630000 CRYPTBASE.DLL
7FFDE1B20000 bcryptPrimitives.dll
7FFDE21E0000 IMM32.DLL
